// Tipos para el módulo de evaluaciones

export enum EstadoEvaluacion {
  PENDIENTE_REVISION = "pendiente_revision",
  REVISADO = "revisado",
  PENDIENTE_MEJORAS = "pendiente_mejoras",
  MEJORAS_APLICADAS = "mejoras_aplicadas"
}

export enum EstadoMejoraAgente {
  PENDIENTE_REVISION_HUMANA = "pendiente_revision_humana",
  APROBADO_PARA_APLICAR = "aprobado_para_aplicar",
  DESCARTADO = "descartado",
  // Alias para compatibilidad con componentes
  PENDIENTE = "pendiente_revision_humana",
  APLICADA = "aprobado_para_aplicar",
  DESCARTADA = "descartado"
}

// Alias para compatibilidad
export const EstadoMejora = EstadoMejoraAgente;

export enum EvaluacionSortBy {
  PUNTUACION_ASC = "puntuacion_asc",
  PUNTUACION_DESC = "puntuacion_desc",
  FECHA_ASC = "fecha_asc",
  FECHA_DESC = "fecha_desc",
  AGENTE_ASC = "agente_asc",
  AGENTE_DESC = "agente_desc"
}

export enum EvaluacionGroupBy {
  WORKFLOW = "workflow",
  AGENTE = "agente",
  ESTADO = "estado",
  NINGUNO = "ninguno"
}

export enum MejoraAgenteSortBy {
  FECHA_CREACION_ASC = "fecha_creacion_asc",
  FECHA_CREACION_DESC = "fecha_creacion_desc",
  FECHA_ACTUALIZACION_ASC = "fecha_actualizacion_asc",
  FECHA_ACTUALIZACION_DESC = "fecha_actualizacion_desc",
  AGENTE_ASC = "agente_asc",
  AGENTE_DESC = "agente_desc",
  ESTADO_ASC = "estado_asc",
  ESTADO_DESC = "estado_desc"
}

// Interfaces base
export interface EvaluacionBase {
  nombre_agente_workflow: string;
  proposito_agente: string;
  criterios_adicionales_evaluacion?: string;
  agente_output: string;
  puntuacion: number;
  argumentos_puntuacion: string;
  sugerencias_mejora?: string;
  execution_id: string;
  n8n_workflow_id: string;
  estado: EstadoEvaluacion;
  agente_id: string;
  nombre_agente_amigable?: string;
  mejora_agente_id?: string;
}

export interface Evaluacion extends EvaluacionBase {
  id: number;
  created_at: string;
}

export interface EvaluacionCreate extends EvaluacionBase {}

export interface EvaluacionUpdate extends Partial<EvaluacionBase> {}

export interface EvaluacionListItem {
  id: number;
  nombre_agente_amigable?: string;
  nombre_agente_workflow: string;
  puntuacion: number;
  estado: EstadoEvaluacion;
  created_at: string;
  n8n_workflow_id: string;
  execution_id: string;
  mejora_agente_id?: string;
}

export interface EvaluacionFilters {
  estado?: EstadoEvaluacion[];
  workflow_tags?: string[];
  agente_id?: string[];
  puntuacion_min?: number;
  puntuacion_max?: number;
  fecha_desde?: string;
  fecha_hasta?: string;
}

export interface EvaluacionListResponse {
  evaluaciones: EvaluacionListItem[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface EvaluacionBatchUpdate {
  evaluacion_ids: number[];
  estado: EstadoEvaluacion;
}

export interface EvaluacionStats {
  total_evaluaciones: number;
  promedio_puntuacion: number;
  evaluaciones_por_estado: Record<EstadoEvaluacion, number>;
  evaluaciones_por_agente: Record<string, number>;
  tendencia_puntuacion: Array<{
    fecha: string;
    puntuacion_promedio: number;
    total_evaluaciones: number;
  }>;
}

export interface EvaluacionDashboardData {
  stats: EvaluacionStats;
  evaluaciones_recientes: EvaluacionListItem[];
  agentes_performance: Array<{
    agente_id: string;
    nombre_agente_amigable: string;
    puntuacion_promedio: number;
    total_evaluaciones: number;
    tendencia: Array<{
      fecha: string;
      puntuacion: number;
    }>;
  }>;
  workflow_performance: Array<{
    workflow_id: string;
    nombre_workflow: string;
    puntuacion_promedio: number;
    total_evaluaciones: number;
  }>;
}

// Interfaces para mejoras de agente
export interface MejoraAgenteBase {
  agente_id: string;
  n8n_workflow_id: string;
  n8n_webhook_url?: string;
  system_prompt_propuesto?: string;
  user_prompt_propuesto?: string;
  explicacion_mejoras: string;
  estado: EstadoMejoraAgente;
  tipo_mejora?: string;
  prioridad?: number;
  descripcion_mejora?: string;
  user_prompt_plantilla?: string;
  user_prompt_mejorado?: string;
  system_prompt_plantilla?: string;
  system_prompt_mejorado?: string;
  nombre_agente_amigable?: string;
}

export interface MejoraAgente extends MejoraAgenteBase {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface MejoraAgenteCreate extends MejoraAgenteBase {}

export interface MejoraAgenteUpdate extends Partial<MejoraAgenteBase> {}

export interface MejoraAgenteListItem {
  id: string;
  agente_id: string;
  n8n_workflow_id: string;
  n8n_webhook_url?: string;
  explicacion_mejoras: string;
  estado: EstadoMejoraAgente;
  created_at: string;
  updated_at: string;
  nombre_agente_amigable?: string;
  evaluaciones_relacionadas?: number;
  tipo_mejora?: string;
  prioridad?: number;
  descripcion_mejora?: string;
  user_prompt_plantilla?: string;
  user_prompt_mejorado?: string;
  system_prompt_plantilla?: string;
  system_prompt_mejorado?: string;
}

export interface MejoraAgenteDetail extends MejoraAgente {
  system_prompt_original?: string;
  user_prompt_original?: string;
  nombre_agente_amigable?: string;
  evaluaciones_relacionadas: number[];
  tipo_mejora?: string;
  prioridad?: number;
  descripcion_mejora?: string;
}

export interface MejoraAgenteFilters {
  estado?: EstadoMejoraAgente[];
  agente_id?: string[];
  n8n_workflow_id?: string[];
  fecha_desde?: string;
  fecha_hasta?: string;
}

export interface MejoraAgenteListResponse {
  mejoras: MejoraAgenteListItem[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface MejoraAgenteApplyRequest {
  mejora_id: string;
  confirmar_aplicacion?: boolean;
}

export interface MejoraAgenteApplyResponse {
  success: boolean;
  message: string;
  webhook_response?: any;
  mejora_actualizada?: MejoraAgente;
}

export interface PromptComparison {
  system_prompt_original?: string;
  system_prompt_propuesto?: string;
  user_prompt_original?: string;
  user_prompt_propuesto?: string;
  explicacion_mejoras: string;
  agente_id: string;
  nombre_agente_amigable?: string;
}

export interface MejoraAgenteStats {
  total_mejoras: number;
  mejoras_por_estado: Record<EstadoMejoraAgente, number>;
  mejoras_por_agente: Record<string, number>;
  mejoras_aplicadas_exitosas: number;
  mejoras_descartadas: number;
}

// Tipos para componentes UI
export interface EvaluacionTableColumn {
  key: keyof EvaluacionListItem | 'actions';
  label: string;
  sortable?: boolean;
  width?: string;
}

export interface MejoraAgenteTableColumn {
  key: keyof MejoraAgenteListItem | 'actions';
  label: string;
  sortable?: boolean;
  width?: string;
}

// Tipos para filtros UI
export interface EvaluacionFilterState {
  estado: EstadoEvaluacion[];
  agente_id: string[];
  puntuacion_range: [number, number];
  fecha_range: [string, string];
  search_text: string;
}

export interface MejoraAgenteFilterState {
  estado: EstadoMejoraAgente[];
  agente_id: string[];
  fecha_range: [string, string];
  search_text: string;
}

// Tipos para dashboard charts
export interface ChartDataPoint {
  fecha: string;
  puntuacion: number;
  total_evaluaciones?: number;
  agente?: string;
  workflow?: string;
}

// Helper functions
export const getAgenteDisplayName = (evaluacion: EvaluacionListItem | Evaluacion): string => {
  return evaluacion.nombre_agente_amigable || evaluacion.nombre_agente_workflow;
};

export interface DashboardChartProps {
  data: ChartDataPoint[];
  title: string;
  height?: number;
  onDataPointClick?: (dataPoint: ChartDataPoint) => void;
}
