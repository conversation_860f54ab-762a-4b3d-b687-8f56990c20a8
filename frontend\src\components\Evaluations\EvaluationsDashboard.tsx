import { useState, useMemo } from 'react';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { Expand, Filter, Eye, EyeOff } from 'lucide-react';
import { useDashboardChartsData } from '../../hooks/useEvaluations';
import { useRealtimeEvaluationsSubscription } from '../../hooks/useRealtimeEvaluations';

export const EvaluationsDashboard = () => {
  // Estados para filtros
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [expandedChart, setExpandedChart] = useState<string | null>(null);

  // Obtener datos del dashboard
  const { data: dashboardData, isLoading, error } = useDashboardChartsData();

  // Activar suscripciones en tiempo real
  useRealtimeEvaluationsSubscription();

  // Preparar datos para filtros
  const { workflowOptions, agentOptions } = useMemo(() => {
    if (!dashboardData?.workflows) {
      return { workflowOptions: [], agentOptions: [] };
    }

    const workflows = dashboardData.workflows.map((w: any) => ({
      id: w.workflow_id,
      label: w.workflow_nombre || `Workflow ${w.workflow_id}`
    }));

    const agents = dashboardData.workflows.flatMap((w: any) =>
      w.agentes.map((a: any) => ({
        id: a.agente_key,
        label: a.nombre_agente_amigable || a.nombre_agente_workflow,
        workflowId: w.workflow_id
      }))
    );

    return { workflowOptions: workflows, agentOptions: agents };
  }, [dashboardData]);

  // Filtrar workflows según selección
  const filteredWorkflows = useMemo(() => {
    if (!dashboardData?.workflows) return [];

    return dashboardData.workflows.filter((workflow: any) => {
      // Si no hay workflows seleccionados, mostrar todos
      if (selectedWorkflows.length === 0) return true;
      return selectedWorkflows.includes(workflow.workflow_id);
    }).map((workflow: any) => ({
      ...workflow,
      agentes: workflow.agentes.filter((agente: any) => {
        // Si no hay agentes seleccionados, mostrar todos
        if (selectedAgents.length === 0) return true;
        return selectedAgents.includes(agente.agente_key);
      })
    })).filter((workflow: any) => workflow.agentes.length > 0);
  }, [dashboardData, selectedWorkflows, selectedAgents]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Cargando datos del dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="text-red-600 font-medium">Error cargando datos</div>
        </div>
        <div className="text-red-600 text-sm mt-1">
          {error instanceof Error ? error.message : 'Error desconocido'}
        </div>
      </div>
    );
  }

  if (!dashboardData?.workflows || dashboardData.workflows.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg mb-2">No hay datos disponibles</div>
        <div className="text-gray-400 text-sm">
          No se encontraron evaluaciones para mostrar en el dashboard.
        </div>
      </div>
    );
  }

  // Funciones para manejar filtros
  const toggleWorkflow = (workflowId: string) => {
    setSelectedWorkflows(prev =>
      prev.includes(workflowId)
        ? prev.filter(id => id !== workflowId)
        : [...prev, workflowId]
    );
  };

  const toggleAgent = (agentKey: string) => {
    setSelectedAgents(prev =>
      prev.includes(agentKey)
        ? prev.filter(key => key !== agentKey)
        : [...prev, agentKey]
    );
  };

  // Componente para gráfico individual de agente
  const AgentChart = ({ agente }: { agente: any, workflowId: string }) => {
    const chartData = useMemo(() => {
      return agente.evaluaciones.map((evaluacion: any) => ({
        fecha: new Date(evaluacion.created_at).toLocaleDateString('es-ES', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        }),
        puntuacion: evaluacion.puntuacion,
        timestamp: new Date(evaluacion.created_at).getTime(),
        created_at: evaluacion.created_at
      })).sort((a: any, b: any) => a.timestamp - b.timestamp);
    }, [agente.evaluaciones]);

    const mejoras = useMemo(() => {
      return agente.mejoras_aplicadas.map((mejora: any) => ({
        fecha: new Date(mejora.fecha).toLocaleDateString('es-ES', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        }),
        timestamp: new Date(mejora.fecha).getTime()
      }));
    }, [agente.mejoras_aplicadas]);

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            {agente.nombre_agente_amigable || agente.nombre_agente_workflow}
          </h3>
          <button
            onClick={() => setExpandedChart(agente.agente_key)}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            title="Ampliar gráfico"
          >
            <Expand className="h-4 w-4" />
          </button>
        </div>

        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="fecha"
                tick={{ fontSize: 11 }}
                angle={-45}
                textAnchor="end"
                height={60}
                interval={0}
              />
              <YAxis
                domain={[0, 100]}
                tick={{ fontSize: 11 }}
                label={{ value: 'Puntuación (%)', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip
                labelFormatter={(label) => `Fecha: ${label}`}
                formatter={(value: any) => [`${value}%`, 'Puntuación']}
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Line
                type="monotone"
                dataKey="puntuacion"
                stroke="#2563eb"
                strokeWidth={2}
                dot={{ fill: '#2563eb', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, fill: '#1d4ed8' }}
              />

              {/* Marcadores de mejoras aplicadas */}
              {mejoras.map((mejora: any, index: number) => (
                <ReferenceLine
                  key={index}
                  x={mejora.fecha}
                  stroke="#ef4444"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  label={{
                    value: "Mejora",
                    position: "top",
                    style: { fontSize: '10px', fill: '#ef4444' }
                  }}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="mt-3 flex items-center justify-between text-sm text-gray-600">
          <span>Evaluaciones: {agente.evaluaciones.length}</span>
          {agente.mejoras_aplicadas.length > 0 && (
            <span className="text-red-600">
              Mejoras aplicadas: {agente.mejoras_aplicadas.length}
            </span>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Filtros globales */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center gap-4 mb-4">
          <Filter className="h-5 w-5 text-gray-500" />
          <h2 className="text-lg font-semibold text-gray-900">Filtros</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Filtro de Workflows */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Workflows ({workflowOptions.length})
            </label>
            <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-3">
              {workflowOptions.map((workflow: any) => (
                <label key={workflow.id} className="flex items-center cursor-pointer hover:bg-gray-50 p-1 rounded">
                  <input
                    type="checkbox"
                    checked={selectedWorkflows.includes(workflow.id)}
                    onChange={() => toggleWorkflow(workflow.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{workflow.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Filtro de Agentes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Agentes ({agentOptions.length})
            </label>
            <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-3">
              {agentOptions.map((agent: any) => (
                <label key={agent.id} className="flex items-center cursor-pointer hover:bg-gray-50 p-1 rounded">
                  <input
                    type="checkbox"
                    checked={selectedAgents.includes(agent.id)}
                    onChange={() => toggleAgent(agent.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{agent.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Indicadores de filtros activos */}
        {(selectedWorkflows.length > 0 || selectedAgents.length > 0) && (
          <div className="mt-4 flex items-center gap-2 text-sm text-blue-600">
            <Eye className="h-4 w-4" />
            <span>
              Filtros activos:
              {selectedWorkflows.length > 0 && ` ${selectedWorkflows.length} workflow(s)`}
              {selectedWorkflows.length > 0 && selectedAgents.length > 0 && ', '}
              {selectedAgents.length > 0 && ` ${selectedAgents.length} agente(s)`}
            </span>
          </div>
        )}
      </div>

      {/* Estadísticas generales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-3xl font-bold text-blue-600">{dashboardData.total_evaluaciones}</div>
          <div className="text-sm text-gray-600">Total Evaluaciones</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-3xl font-bold text-green-600">{dashboardData.total_workflows}</div>
          <div className="text-sm text-gray-600">Workflows</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-3xl font-bold text-purple-600">{dashboardData.total_agentes}</div>
          <div className="text-sm text-gray-600">Agentes Únicos</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-3xl font-bold text-orange-600">
            {dashboardData.workflows?.reduce((acc: number, w: any) =>
              acc + (w.agentes?.reduce((agentAcc: number, a: any) =>
                agentAcc + (a.mejoras_aplicadas?.length || 0), 0) || 0), 0) || 0}
          </div>
          <div className="text-sm text-gray-600">Mejoras Aplicadas</div>
        </div>
      </div>

      {/* Secciones por Workflow */}
      {filteredWorkflows.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <EyeOff className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay datos para mostrar</h3>
          <p className="text-gray-600">
            {selectedWorkflows.length > 0 || selectedAgents.length > 0
              ? 'Los filtros aplicados no coinciden con ningún dato disponible.'
              : 'No se encontraron evaluaciones para mostrar en el dashboard.'}
          </p>
        </div>
      ) : (
        filteredWorkflows.map((workflow: any) => (
          <div key={workflow.workflow_id} className="space-y-4">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg px-6 py-4">
              <h3 className="text-xl font-semibold text-gray-900">
                {workflow.workflow_nombre || `Workflow ${workflow.workflow_id}`}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {workflow.agentes.length} agente{workflow.agentes.length !== 1 ? 's' : ''}
                {workflow.agentes.length > 0 && ` • ${workflow.agentes.reduce((acc: number, a: any) => acc + a.evaluaciones.length, 0)} evaluaciones totales`}
              </p>
            </div>

            {/* Cuadrícula de gráficos de agentes */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {workflow.agentes.map((agente: any) => (
                <AgentChart
                  key={agente.agente_key}
                  agente={agente}
                  workflowId={workflow.workflow_id}
                />
              ))}
            </div>
          </div>
        ))
      )}

      {/* Modal para gráfico ampliado */}
      {expandedChart && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-semibold text-gray-900">
                  Gráfico Ampliado
                </h2>
                <button
                  onClick={() => setExpandedChart(null)}
                  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Cerrar"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {(() => {
                const agente = filteredWorkflows
                  .flatMap(w => w.agentes)
                  .find(a => a.agente_key === expandedChart);

                if (!agente) return null;

                return (
                  <div className="h-96">
                    <AgentChart agente={agente} workflowId="" />
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
