import React from 'react';
import { X, Calendar, User, BarChart3, FileText, CheckCircle, Clock, AlertTriangle, Lightbulb } from 'lucide-react';
import { EvaluacionListItem, EstadoEvaluacion } from '../../types/evaluaciones';

interface EvaluationDetailModalProps {
  evaluacion: EvaluacionListItem | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdateEstado?: (id: number, estado: EstadoEvaluacion) => void;
}

export const EvaluationDetailModal: React.FC<EvaluationDetailModalProps> = ({
  evaluacion,
  isOpen,
  onClose,
  onUpdateEstado
}) => {
  if (!isOpen || !evaluacion) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEstadoBadge = (estado: EstadoEvaluacion) => {
    const configs = {
      [EstadoEvaluacion.PENDIENTE_REVISION]: {
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: Clock,
        label: 'Pendiente Revisión'
      },
      [EstadoEvaluacion.REVISADO]: {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircle,
        label: 'Revisado'
      },
      [EstadoEvaluacion.PENDIENTE_MEJORAS]: {
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: AlertTriangle,
        label: 'Pendiente Mejoras'
      },
      [EstadoEvaluacion.MEJORAS_APLICADAS]: {
        color: 'bg-purple-100 text-purple-800 border-purple-200',
        icon: CheckCircle,
        label: 'Mejoras Aplicadas'
      }
    };

    const config = configs[estado];
    const Icon = config.icon;

    return (
      <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-full border ${config.color}`}>
        <Icon className="w-4 h-4" />
        <span className="text-sm font-medium">{config.label}</span>
      </div>
    );
  };

  const getPuntuacionColor = (puntuacion: number) => {
    if (puntuacion >= 8) return 'text-green-600';
    if (puntuacion >= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handleEstadoChange = (nuevoEstado: EstadoEvaluacion) => {
    if (onUpdateEstado) {
      onUpdateEstado(evaluacion.id, nuevoEstado);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Detalle de Evaluación</h2>
            <p className="text-sm text-gray-500 mt-1">ID: {evaluacion.id}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Información Principal */}
            <div className="lg:col-span-2 space-y-6">
              {/* Bloque Workflow */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Workflow
                </h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-500">ID del Workflow:</span>
                    <p className="text-gray-900">{evaluacion.n8n_workflow_id}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">ID de Ejecución:</span>
                    <p className="text-gray-900">{evaluacion.execution_id}</p>
                  </div>
                </div>
              </div>

              {/* Bloque Agente */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  Agente
                </h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Nombre:</span>
                    <p className="text-gray-900">{evaluacion.nombre_agente_amigable}</p>
                  </div>
                </div>
              </div>

              {/* Bloque Evaluación */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Evaluación
                </h3>
                <div className="space-y-4">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Puntuación:</span>
                    <div className={`text-3xl font-bold ${getPuntuacionColor(evaluacion.puntuacion)} mt-1`}>
                      {evaluacion.puntuacion}/10
                    </div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-500">Información adicional:</span>
                    <div className="mt-2 p-3 bg-white rounded border">
                      <p className="text-gray-600 text-sm">
                        Los detalles completos de feedback y sugerencias se mostrarán cuando estén disponibles en la API.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bloque Sugerencias de Mejora */}
              {evaluacion.sugerencias_mejora && evaluacion.sugerencias_mejora.trim() !== '' && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <Lightbulb className="w-5 h-5 mr-2" />
                    Sugerencias de Mejora
                  </h3>
                  <div className="bg-white rounded border p-3">
                    <p className="text-gray-700 text-sm whitespace-pre-wrap leading-relaxed">
                      {evaluacion.sugerencias_mejora}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Panel Lateral */}
            <div className="space-y-6">
              {/* Estado y Acciones */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Estado</h3>
                
                <div className="mb-4">
                  {getEstadoBadge(evaluacion.estado)}
                </div>

                {onUpdateEstado && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">Cambiar estado:</p>
                    <div className="space-y-2">
                      {Object.values(EstadoEvaluacion).map(estado => (
                        <button
                          key={estado}
                          onClick={() => handleEstadoChange(estado)}
                          disabled={estado === evaluacion.estado}
                          className={`w-full text-left px-3 py-2 text-sm rounded border transition-colors ${
                            estado === evaluacion.estado
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300'
                          }`}
                        >
                          {estado.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Información Adicional */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Información</h3>
                
                <div className="space-y-3">
                  <div className="flex items-center text-sm">
                    <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                    <div>
                      <p className="text-gray-500">Fecha de evaluación</p>
                      <p className="text-gray-900">{formatDate(evaluacion.created_at)}</p>
                    </div>
                  </div>

                  <div className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-gray-400 mr-2" />
                    <div>
                      <p className="text-gray-500">Sugerencias disponibles</p>
                      <p className="text-gray-900">
                        {evaluacion.sugerencias_mejora && evaluacion.sugerencias_mejora.trim() !== '' ? 'Sí' : 'No'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Acciones */}
              {evaluacion.sugerencias_mejora && evaluacion.sugerencias_mejora.trim() !== '' && (
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Acciones</h3>
                  <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Ver Sugerencias de Mejora
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cerrar
          </button>
        </div>
      </div>
    </div>
  );
};
