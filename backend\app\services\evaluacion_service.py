from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
from decimal import Decimal
import math

from app.core.database import get_supabase_client
from app.models.evaluacion import (
    Evaluacion,
    EvaluacionCreate,
    EvaluacionUpdate,
    EvaluacionListItem,
    EvaluacionListResponse,
    EvaluacionFilters,
    EvaluacionSortBy,
    EvaluacionGroupBy,
    EvaluacionBatchUpdate,
    EvaluacionStats,
    EvaluacionDashboardData,
    EstadoEvaluacion
)

class EvaluacionService:
    """Servicio para manejar operaciones de evaluaciones."""

    def __init__(self):
        self.supabase = None
    
    async def create_evaluacion(self, evaluacion_data: EvaluacionCreate, user_id: UUID) -> Evaluacion:
        """Crear una nueva evaluación."""
        try:
            supabase = await get_supabase_client()
            # Preparar datos para inserción
            insert_data = evaluacion_data.model_dump()
            insert_data['user_id'] = str(user_id)  # Agregar user_id si es necesario

            result = supabase.table('evaluaciones').insert(insert_data).execute()
            
            if not result.data:
                raise Exception("Error al crear la evaluación")
            
            return Evaluacion(**result.data[0])
        except Exception as e:
            raise Exception(f"Error al crear evaluación: {str(e)}")
    
    async def get_evaluacion_by_id(self, evaluacion_id: int, user_id: UUID) -> Optional[Evaluacion]:
        """Obtener una evaluación por ID."""
        try:
            supabase = await get_supabase_client()
            result = supabase.table('evaluaciones').select('*').eq('id', evaluacion_id).execute()
            
            if not result.data:
                return None
            
            return Evaluacion(**result.data[0])
        except Exception as e:
            raise Exception(f"Error al obtener evaluación: {str(e)}")
    
    async def update_evaluacion(self, evaluacion_id: int, evaluacion_data: EvaluacionUpdate, user_id: UUID) -> Optional[Evaluacion]:
        """Actualizar una evaluación."""
        try:
            supabase = await get_supabase_client()
            # Filtrar campos no nulos
            update_data = {k: v for k, v in evaluacion_data.model_dump().items() if v is not None}

            if not update_data:
                # Si no hay datos para actualizar, obtener la evaluación actual
                return await self.get_evaluacion_by_id(evaluacion_id, user_id)

            result = supabase.table('evaluaciones').update(update_data).eq('id', evaluacion_id).execute()
            
            if not result.data:
                return None
            
            return Evaluacion(**result.data[0])
        except Exception as e:
            raise Exception(f"Error al actualizar evaluación: {str(e)}")
    
    async def delete_evaluacion(self, evaluacion_id: int, user_id: UUID) -> bool:
        """Eliminar una evaluación."""
        try:
            result = self.supabase.table('evaluaciones').delete().eq('id', evaluacion_id).execute()
            return len(result.data) > 0
        except Exception as e:
            raise Exception(f"Error al eliminar evaluación: {str(e)}")
    
    async def list_evaluaciones(
        self,
        user_id: UUID,
        page: int = 1,
        page_size: int = 20,
        filters: Optional[EvaluacionFilters] = None,
        sort_by: EvaluacionSortBy = EvaluacionSortBy.FECHA_DESC,
        group_by: EvaluacionGroupBy = EvaluacionGroupBy.NINGUNO
    ) -> EvaluacionListResponse:
        """Listar evaluaciones con filtros, ordenamiento y paginación."""
        try:
            supabase = await get_supabase_client()
            # Construir query base
            query = supabase.table('evaluaciones').select(
                'id, nombre_agente_amigable, nombre_agente_workflow, puntuacion, estado, created_at, '
                'n8n_workflow_id, execution_id, mejora_agente_id',
                count='exact'
            )
            
            # Aplicar filtros
            if filters:
                if filters.estado:
                    query = query.in_('estado', [estado.value for estado in filters.estado])
                if filters.agente_id:
                    query = query.in_('agente_id', filters.agente_id)
                if filters.puntuacion_min is not None:
                    query = query.gte('puntuacion', float(filters.puntuacion_min))
                if filters.puntuacion_max is not None:
                    query = query.lte('puntuacion', float(filters.puntuacion_max))
                if filters.fecha_desde:
                    query = query.gte('created_at', filters.fecha_desde.isoformat())
                if filters.fecha_hasta:
                    query = query.lte('created_at', filters.fecha_hasta.isoformat())
            
            # Aplicar ordenamiento
            order_column, order_direction = self._parse_sort_by(sort_by)
            query = query.order(order_column, desc=(order_direction == 'desc'))
            
            # Aplicar paginación
            offset = (page - 1) * page_size
            query = query.range(offset, offset + page_size - 1)
            
            result = query.execute()
            
            # Convertir resultados
            evaluaciones = [EvaluacionListItem(**item) for item in result.data]
            total = result.count or 0
            total_pages = math.ceil(total / page_size)
            
            return EvaluacionListResponse(
                evaluaciones=evaluaciones,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )
        except Exception as e:
            raise Exception(f"Error al listar evaluaciones: {str(e)}")
    
    async def batch_update_evaluaciones(self, batch_update: EvaluacionBatchUpdate, user_id: UUID) -> List[Evaluacion]:
        """Actualizar múltiples evaluaciones en lote."""
        try:
            result = self.supabase.table('evaluaciones').update({
                'estado': batch_update.estado.value
            }).in_('id', batch_update.evaluacion_ids).execute()
            
            return [Evaluacion(**item) for item in result.data]
        except Exception as e:
            raise Exception(f"Error en actualización en lote: {str(e)}")
    
    async def get_evaluacion_stats(self, user_id: UUID) -> EvaluacionStats:
        """Obtener estadísticas de evaluaciones."""
        try:
            supabase = await get_supabase_client()
            # Obtener todas las evaluaciones para calcular estadísticas
            result = supabase.table('evaluaciones').select(
                'id, puntuacion, estado, agente_id, nombre_agente_amigable, created_at'
            ).execute()
            
            evaluaciones = result.data
            total_evaluaciones = len(evaluaciones)
            
            if total_evaluaciones == 0:
                return EvaluacionStats(
                    total_evaluaciones=0,
                    promedio_puntuacion=Decimal('0'),
                    evaluaciones_por_estado={},
                    evaluaciones_por_agente={},
                    tendencia_puntuacion=[]
                )
            
            # Calcular promedio de puntuación
            puntuaciones = [Decimal(str(ev['puntuacion'])) for ev in evaluaciones]
            promedio_puntuacion = sum(puntuaciones) / len(puntuaciones)
            
            # Contar por estado
            evaluaciones_por_estado = {}
            for estado in EstadoEvaluacion:
                evaluaciones_por_estado[estado] = len([ev for ev in evaluaciones if ev['estado'] == estado.value])
            
            # Contar por agente
            evaluaciones_por_agente = {}
            for ev in evaluaciones:
                agente = ev['nombre_agente_amigable'] or ev['agente_id']
                evaluaciones_por_agente[agente] = evaluaciones_por_agente.get(agente, 0) + 1
            
            # Tendencia de puntuación (últimos 30 días)
            # Simplificado - en producción se haría una query más eficiente
            tendencia_puntuacion = []
            
            return EvaluacionStats(
                total_evaluaciones=total_evaluaciones,
                promedio_puntuacion=promedio_puntuacion,
                evaluaciones_por_estado=evaluaciones_por_estado,
                evaluaciones_por_agente=evaluaciones_por_agente,
                tendencia_puntuacion=tendencia_puntuacion
            )
        except Exception as e:
            raise Exception(f"Error al obtener estadísticas: {str(e)}")
    
    def _parse_sort_by(self, sort_by: EvaluacionSortBy) -> tuple[str, str]:
        """Parsear el campo de ordenamiento."""
        sort_mapping = {
            EvaluacionSortBy.PUNTUACION_ASC: ('puntuacion', 'asc'),
            EvaluacionSortBy.PUNTUACION_DESC: ('puntuacion', 'desc'),
            EvaluacionSortBy.FECHA_ASC: ('created_at', 'asc'),
            EvaluacionSortBy.FECHA_DESC: ('created_at', 'desc'),
            EvaluacionSortBy.AGENTE_ASC: ('nombre_agente_amigable', 'asc'),
            EvaluacionSortBy.AGENTE_DESC: ('nombre_agente_amigable', 'desc'),
        }
        return sort_mapping.get(sort_by, ('created_at', 'desc'))

    async def get_dashboard_charts_data(self, user_id: UUID):
        """Obtener datos para los gráficos del dashboard agrupados por workflow y agente."""
        try:
            supabase = await get_supabase_client()

            # Obtener todas las evaluaciones con información de mejoras
            # Nota: La tabla evaluaciones no tiene columna user_id, así que obtenemos todos los datos
            evaluaciones_result = supabase.table('evaluaciones').select(
                'id, n8n_workflow_id, nombre_agente_workflow, nombre_agente_amigable, '
                'puntuacion, created_at, mejora_agente_id, '
                'mejoras_agentes(id, created_at)'
            ).order('n8n_workflow_id').order('nombre_agente_workflow').order('created_at').execute()

            # Obtener información de workflows por separado
            workflows_result = supabase.table('workflows').select(
                'id, n8n_workflow_id, nombre, descripcion'
            ).execute()

            if not evaluaciones_result.data:
                return {
                    "workflows": [],
                    "total_evaluaciones": 0,
                    "total_agentes": 0,
                    "total_workflows": 0
                }

            # Crear diccionario de workflows por n8n_workflow_id
            workflows_dict = {}
            if workflows_result.data:
                for workflow in workflows_result.data:
                    workflows_dict[workflow['n8n_workflow_id']] = {
                        'nombre': workflow['nombre'],
                        'descripcion': workflow['descripcion']
                    }
                print(f"DEBUG: workflows_dict = {workflows_dict}")  # Debug log

            # Agrupar datos por workflow y agente
            workflows_data = {}
            total_evaluaciones = len(evaluaciones_result.data)
            agentes_unicos = set()

            for row in evaluaciones_result.data:
                workflow_id = row['n8n_workflow_id']
                agente_key = f"{workflow_id}_{row['nombre_agente_workflow']}"
                agentes_unicos.add(agente_key)

                # Obtener información del workflow desde el diccionario
                workflow_info = workflows_dict.get(workflow_id, {})
                workflow_nombre = workflow_info.get('nombre')
                workflow_descripcion = workflow_info.get('descripcion')

                if workflow_id not in workflows_data:
                    workflows_data[workflow_id] = {
                        "workflow_id": workflow_id,
                        "workflow_nombre": workflow_nombre,
                        "workflow_descripcion": workflow_descripcion,
                        "agentes": {}
                    }

                if agente_key not in workflows_data[workflow_id]["agentes"]:
                    workflows_data[workflow_id]["agentes"][agente_key] = {
                        "nombre_agente_workflow": row['nombre_agente_workflow'],
                        "nombre_agente_amigable": row['nombre_agente_amigable'],
                        "evaluaciones": [],
                        "mejoras_aplicadas": []
                    }

                # Agregar evaluación
                workflows_data[workflow_id]["agentes"][agente_key]["evaluaciones"].append({
                    "id": row['id'],
                    "puntuacion": row['puntuacion'],
                    "created_at": row['created_at']
                })

                # Agregar mejora si existe
                if row['mejora_agente_id'] and row.get('mejoras_agentes'):
                    mejora_data = row['mejoras_agentes']
                    if mejora_data and isinstance(mejora_data, dict):
                        workflows_data[workflow_id]["agentes"][agente_key]["mejoras_aplicadas"].append({
                            "mejora_id": row['mejora_agente_id'],
                            "fecha": mejora_data.get('created_at')
                        })

            # Convertir a lista
            workflows_list = []
            for workflow_id, workflow_data in workflows_data.items():
                agentes_list = []
                for agente_key, agente_data in workflow_data["agentes"].items():
                    agentes_list.append({
                        "agente_key": agente_key,
                        **agente_data
                    })

                workflows_list.append({
                    "workflow_id": workflow_id,
                    "workflow_nombre": workflow_data["workflow_nombre"],
                    "workflow_descripcion": workflow_data["workflow_descripcion"],
                    "agentes": agentes_list
                })

            result_data = {
                "workflows": workflows_list,
                "total_evaluaciones": total_evaluaciones,
                "total_agentes": len(agentes_unicos),
                "total_workflows": len(workflows_data)
            }
            print(f"DEBUG: Final result = {result_data}")  # Debug log
            return result_data

        except Exception as e:
            raise Exception(f"Error obteniendo datos del dashboard: {str(e)}")
