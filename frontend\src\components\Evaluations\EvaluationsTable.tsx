import React from 'react';
import { ChevronUp, ChevronDown, MoreVertical, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { EvaluacionListItem, EstadoEvaluacion, EvaluacionSortBy, EvaluacionGroupBy } from '../../types/evaluaciones';

interface EvaluationsTableProps {
  evaluaciones: EvaluacionListItem[];
  selectedIds: number[];
  onSelectionChange: (ids: number[]) => void;
  onRowClick: (evaluacion: EvaluacionListItem) => void;
  sortBy: EvaluacionSortBy;
  onSortChange: (sortBy: EvaluacionSortBy) => void;
  groupBy: EvaluacionGroupBy;
  onGroupChange: (groupBy: EvaluacionGroupBy) => void;
  isLoading?: boolean;
}

export const EvaluationsTable: React.FC<EvaluationsTableProps> = ({
  evaluaciones,
  selectedIds,
  onSelectionChange,
  onRowClick,
  sortBy,
  onSortChange,
  groupBy,
  onGroupChange,
  isLoading = false
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEstadoBadge = (estado: EstadoEvaluacion) => {
    const configs = {
      [EstadoEvaluacion.PENDIENTE_REVISION]: {
        color: 'bg-yellow-100 text-yellow-800',
        icon: Clock,
        label: 'Pendiente'
      },
      [EstadoEvaluacion.REVISADO]: {
        color: 'bg-green-100 text-green-800',
        icon: CheckCircle,
        label: 'Revisado'
      },
      [EstadoEvaluacion.PENDIENTE_MEJORAS]: {
        color: 'bg-blue-100 text-blue-800',
        icon: AlertTriangle,
        label: 'Mejoras'
      },
      [EstadoEvaluacion.MEJORAS_APLICADAS]: {
        color: 'bg-purple-100 text-purple-800',
        icon: CheckCircle,
        label: 'Aplicadas'
      }
    };

    const config = configs[estado];
    const Icon = config.icon;

    return (
      <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        <span>{config.label}</span>
      </div>
    );
  };

  const getPuntuacionColor = (puntuacion: number) => {
    if (puntuacion >= 8) return 'text-green-600 bg-green-50';
    if (puntuacion >= 6) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(evaluaciones.map(e => e.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectRow = (id: number, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, id]);
    } else {
      onSelectionChange(selectedIds.filter(selectedId => selectedId !== id));
    }
  };

  const getSortIcon = (column: string) => {
    if (sortBy === `${column}_asc`) return <ChevronUp className="w-4 h-4" />;
    if (sortBy === `${column}_desc`) return <ChevronDown className="w-4 h-4" />;
    return null;
  };

  const handleSort = (column: string) => {
    const currentSort = sortBy;
    let newSort: EvaluacionSortBy;

    if (currentSort === `${column}_asc`) {
      newSort = `${column}_desc` as EvaluacionSortBy;
    } else {
      newSort = `${column}_asc` as EvaluacionSortBy;
    }

    onSortChange(newSort);
  };

  const isAllSelected = evaluaciones.length > 0 && selectedIds.length === evaluaciones.length;
  const isIndeterminate = selectedIds.length > 0 && selectedIds.length < evaluaciones.length;

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">Cargando evaluaciones...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header con controles de agrupación */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Agrupar por:</span>
            <select
              value={groupBy}
              onChange={(e) => onGroupChange(e.target.value as EvaluacionGroupBy)}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="ninguno">Sin agrupar</option>
              <option value="workflow">Workflow</option>
              <option value="agente">Agente</option>
              <option value="estado">Estado</option>
            </select>
          </div>
          
          <div className="text-sm text-gray-500">
            {evaluaciones.length} evaluaciones
            {selectedIds.length > 0 && ` (${selectedIds.length} seleccionadas)`}
          </div>
        </div>
      </div>

      {/* Tabla */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={(input) => {
                    if (input) input.indeterminate = isIndeterminate;
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button
                  onClick={() => handleSort('workflow')}
                  className="flex items-center space-x-1 hover:text-gray-700"
                >
                  <span>Workflow</span>
                  {getSortIcon('workflow')}
                </button>
              </th>
              
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button
                  onClick={() => handleSort('puntuacion')}
                  className="flex items-center space-x-1 hover:text-gray-700"
                >
                  <span>Puntuación</span>
                  {getSortIcon('puntuacion')}
                </button>
              </th>
              
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button
                  onClick={() => handleSort('agente')}
                  className="flex items-center space-x-1 hover:text-gray-700"
                >
                  <span>Agente</span>
                  {getSortIcon('agente')}
                </button>
              </th>
              
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Estado
              </th>
              
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Sugerencias
              </th>
              
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button
                  onClick={() => handleSort('fecha')}
                  className="flex items-center space-x-1 hover:text-gray-700"
                >
                  <span>Fecha</span>
                  {getSortIcon('fecha')}
                </button>
              </th>
              
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Acciones
              </th>
            </tr>
          </thead>
          
          <tbody className="bg-white divide-y divide-gray-200">
            {evaluaciones.map((evaluacion) => (
              <tr
                key={evaluacion.id}
                onClick={() => onRowClick(evaluacion)}
                className="hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedIds.includes(evaluacion.id)}
                    onChange={(e) => {
                      e.stopPropagation();
                      handleSelectRow(evaluacion.id, e.target.checked);
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {evaluacion.n8n_workflow_id}
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${getPuntuacionColor(evaluacion.puntuacion)}`}>
                    {evaluacion.puntuacion}/10
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {evaluacion.nombre_agente_amigable}
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  {getEstadoBadge(evaluacion.estado)}
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {evaluacion.mejora_agente_id ? (
                      <span className="text-green-600 font-medium">Sí</span>
                    ) : (
                      <span className="text-gray-400">No</span>
                    )}
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">
                    {formatDate(evaluacion.created_at)}
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // TODO: Implementar menú de acciones
                    }}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <MoreVertical className="w-4 h-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {evaluaciones.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No hay evaluaciones para mostrar</p>
        </div>
      )}
    </div>
  );
};
