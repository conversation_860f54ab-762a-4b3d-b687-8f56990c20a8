import { format } from 'date-fns';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { 
  ArrowLeft,
  Bot,
  Target,
  MessageSquare,
  BarChart3,
  Lightbulb,
  ExternalLink,
  CheckCircle,
  XCircle,
  Clock,
  Settings
} from 'lucide-react';
import { Button } from '../UI/button';
import { Badge } from '../UI/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../UI/card';
import { Separator } from '../UI/separator';
import { ScrollArea } from '../UI/scroll-area';
import {
  Evaluacion,
  EstadoEvaluacion
} from '../../types/evaluaciones';

interface EvaluationDetailProps {
  evaluacion: Evaluacion;
  onBack: () => void;
  onMarkReviewed: (id: number) => void;
  onDiscard: (id: number) => void;
  isUpdating?: boolean;
}

const getEstadoBadge = (estado: EstadoEvaluacion) => {
  const variants = {
    [EstadoEvaluacion.PENDIENTE_REVISION]: {
      variant: 'secondary' as const,
      icon: Clock,
      label: 'Pendiente Revisión'
    },
    [EstadoEvaluacion.REVISADO]: {
      variant: 'default' as const,
      icon: CheckCircle,
      label: 'Revisado'
    },
    [EstadoEvaluacion.PENDIENTE_MEJORAS]: {
      variant: 'destructive' as const,
      icon: Settings,
      label: 'Pendiente Mejoras'
    },
    [EstadoEvaluacion.MEJORAS_APLICADAS]: {
      variant: 'default' as const,
      icon: CheckCircle,
      label: 'Mejoras Aplicadas'
    },
  };

  const config = variants[estado];
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
};

const getPuntuacionColor = (puntuacion: number) => {
  if (puntuacion >= 8) return 'text-green-600';
  if (puntuacion >= 6) return 'text-yellow-600';
  return 'text-red-600';
};

export const EvaluationDetail: React.FC<EvaluationDetailProps> = ({
  evaluacion,
  onBack,
  onMarkReviewed,
  onDiscard,
  isUpdating = false,
}) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{evaluacion.nombre_agente_amigable}</h1>
            <p className="text-gray-500">
              Evaluación del {format(new Date(evaluacion.created_at), 'dd/MM/yyyy HH:mm', { locale: es })}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {getEstadoBadge(evaluacion.estado)}
          {evaluacion.estado === EstadoEvaluacion.PENDIENTE_REVISION && (
            <div className="flex gap-2">
              <Button
                onClick={() => onMarkReviewed(evaluacion.id)}
                disabled={isUpdating}
                size="sm"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Marcar Revisada
              </Button>
              <Button
                variant="outline"
                onClick={() => onDiscard(evaluacion.id)}
                disabled={isUpdating}
                size="sm"
              >
                <XCircle className="h-4 w-4 mr-2" />
                Descartar
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Puntuación */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Puntuación
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div className={`text-4xl font-bold ${getPuntuacionColor(evaluacion.puntuacion)}`}>
                  {evaluacion.puntuacion.toFixed(1)}/10
                </div>
                <div className="flex-1">
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full ${
                        evaluacion.puntuacion >= 8 ? 'bg-green-500' :
                        evaluacion.puntuacion >= 6 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${(evaluacion.puntuacion / 10) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Argumentos de Puntuación */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Argumentos de Puntuación
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-32">
                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                  {evaluacion.argumentos_puntuacion}
                </p>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Output del Agente */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-5 w-5" />
                Output del Agente
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-48">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <pre className="text-sm whitespace-pre-wrap font-mono">
                    {evaluacion.agente_output}
                  </pre>
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Sugerencias de Mejora */}
          {evaluacion.sugerencias_mejora && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5" />
                  Sugerencias de Mejora
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-32">
                  <p className="text-sm leading-relaxed whitespace-pre-wrap">
                    {evaluacion.sugerencias_mejora}
                  </p>
                </ScrollArea>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Metadata */}
        <div className="space-y-6">
          {/* Información del Agente */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-5 w-5" />
                Información del Agente
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Nombre Amigable</label>
                <p className="text-sm">{evaluacion.nombre_agente_amigable}</p>
              </div>
              
              <Separator />
              
              <div>
                <label className="text-sm font-medium text-gray-500">Nombre Workflow</label>
                <p className="text-sm font-mono">{evaluacion.nombre_agente_workflow}</p>
              </div>
              
              <Separator />
              
              <div>
                <label className="text-sm font-medium text-gray-500">ID Agente</label>
                <p className="text-sm font-mono">{evaluacion.agente_id}</p>
              </div>
            </CardContent>
          </Card>

          {/* Propósito del Agente */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Propósito
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm leading-relaxed">
                {evaluacion.proposito_agente}
              </p>
            </CardContent>
          </Card>

          {/* Criterios Adicionales */}
          {evaluacion.criterios_adicionales_evaluacion && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Criterios Adicionales</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm leading-relaxed">
                  {evaluacion.criterios_adicionales_evaluacion}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Información Técnica */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Información Técnica</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-xs font-medium text-gray-500">Workflow ID</label>
                <p className="text-xs font-mono">{evaluacion.n8n_workflow_id}</p>
              </div>
              
              <div>
                <label className="text-xs font-medium text-gray-500">Execution ID</label>
                <p className="text-xs font-mono">{evaluacion.execution_id}</p>
              </div>
              
              <div>
                <label className="text-xs font-medium text-gray-500">Fecha Creación</label>
                <p className="text-xs">
                  {format(new Date(evaluacion.created_at), 'dd/MM/yyyy HH:mm:ss', { locale: es })}
                </p>
              </div>

              {evaluacion.mejora_agente_id && (
                <>
                  <Separator />
                  <div>
                    <label className="text-xs font-medium text-gray-500">Mejora Asociada</label>
                    <div className="flex items-center gap-2 mt-1">
                      <p className="text-xs font-mono">{evaluacion.mejora_agente_id}</p>
                      <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
