import React from 'react';
import { Filter, X, Search, ChevronDown } from 'lucide-react';
import { EstadoEvaluacion } from '../../types/evaluaciones';
import { useEvaluacionFilterOptions } from '../../hooks/useEvaluations';

interface EvaluationsFiltersProps {
  filters: any; // TODO: Usar tipo correcto cuando esté disponible
  onFilterChange: (key: string, value: any) => void;
  onClearFilters: () => void;
}

export const EvaluationsFilters: React.FC<EvaluationsFiltersProps> = ({
  filters,
  onFilterChange,
  onClearFilters
}) => {
  const { data: filterOptions, isLoading: isLoadingOptions } = useEvaluacionFilterOptions();

  const estadoOptions = [
    { value: EstadoEvaluacion.PENDIENTE_REVISION, label: 'Pendiente Revisión' },
    { value: EstadoEvaluacion.REVISADO, label: 'Revisado' },
    { value: EstadoEvaluacion.PENDIENTE_MEJORAS, label: 'Pendiente Mejoras' },
    { value: EstadoEvaluacion.MEJORAS_APLICADAS, label: 'Mejoras Aplicadas' }
  ];

  const handleEstadoChange = (estado: EstadoEvaluacion, checked: boolean) => {
    const currentEstados = filters.estado || [];
    const newEstados = checked
      ? [...currentEstados, estado]
      : currentEstados.filter((e: EstadoEvaluacion) => e !== estado);

    onFilterChange('estado', newEstados);
  };

  const handleWorkflowChange = (workflowId: string, checked: boolean) => {
    const currentWorkflows = filters.workflow_ids || [];
    const newWorkflows = checked
      ? [...currentWorkflows, workflowId]
      : currentWorkflows.filter((id: string) => id !== workflowId);

    onFilterChange('workflow_ids', newWorkflows);
  };

  const handleEtiquetaChange = (etiquetaId: string, checked: boolean) => {
    const currentEtiquetas = filters.workflow_tags || [];
    const newEtiquetas = checked
      ? [...currentEtiquetas, etiquetaId]
      : currentEtiquetas.filter((id: string) => id !== etiquetaId);

    onFilterChange('workflow_tags', newEtiquetas);
  };

  const hasActiveFilters = () => {
    return (
      (filters.estado && filters.estado.length > 0) ||
      filters.solo_con_sugerencias ||
      (filters.workflow_tags && filters.workflow_tags.length > 0) ||
      filters.puntuacion_min ||
      filters.puntuacion_max ||
      filters.fecha_desde ||
      filters.fecha_hasta
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Filter className="w-5 h-5 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900">Filtros</h3>
        </div>
        
        {hasActiveFilters() && (
          <button
            onClick={onClearFilters}
            className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X className="w-4 h-4" />
            <span>Limpiar filtros</span>
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Filtro de Estado */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Estado
          </label>
          <div className="space-y-2">
            {estadoOptions.map((option) => (
              <label key={option.value} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.estado?.includes(option.value) || false}
                  onChange={(e) => handleEstadoChange(option.value, e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Filtro Solo con Sugerencias */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Opciones
          </label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={filters.solo_con_sugerencias || false}
                onChange={(e) => onFilterChange('solo_con_sugerencias', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Solo con sugerencias</span>
            </label>
          </div>
        </div>

        {/* Filtro de Puntuación */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Puntuación
          </label>
          <div className="space-y-2">
            <div className="flex space-x-2">
              <input
                type="number"
                min="0"
                max="10"
                step="0.1"
                placeholder="Mín"
                value={filters.puntuacion_min || ''}
                onChange={(e) => onFilterChange('puntuacion_min', e.target.value ? parseFloat(e.target.value) : undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <input
                type="number"
                min="0"
                max="10"
                step="0.1"
                placeholder="Máx"
                value={filters.puntuacion_max || ''}
                onChange={(e) => onFilterChange('puntuacion_max', e.target.value ? parseFloat(e.target.value) : undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Filtro de Fechas */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fechas
          </label>
          <div className="space-y-2">
            <input
              type="date"
              placeholder="Desde"
              value={filters.fecha_desde || ''}
              onChange={(e) => onFilterChange('fecha_desde', e.target.value || undefined)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
            <input
              type="date"
              placeholder="Hasta"
              value={filters.fecha_hasta || ''}
              onChange={(e) => onFilterChange('fecha_hasta', e.target.value || undefined)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Filtro de Workflows */}
      <div className="mt-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Workflows
        </label>
        {isLoadingOptions ? (
          <div className="text-sm text-gray-500">Cargando workflows...</div>
        ) : (
          <div className="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-2">
            {filterOptions?.workflows?.map((workflow) => (
              <label key={workflow.id} className="flex items-center space-x-2 text-sm">
                <input
                  type="checkbox"
                  checked={filters.workflow_ids?.includes(workflow.id) || false}
                  onChange={(e) => handleWorkflowChange(workflow.id, e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-gray-700 truncate">{workflow.nombre}</span>
              </label>
            ))}
            {(!filterOptions?.workflows || filterOptions.workflows.length === 0) && (
              <div className="text-sm text-gray-500">No hay workflows disponibles</div>
            )}
          </div>
        )}
      </div>

      {/* Filtro de Workflow Tags */}
      <div className="mt-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Etiquetas de Workflow
        </label>
        {isLoadingOptions ? (
          <div className="text-sm text-gray-500">Cargando etiquetas...</div>
        ) : (
          <div className="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-2">
            {filterOptions?.etiquetas?.map((etiqueta) => (
              <label key={etiqueta.id} className="flex items-center space-x-2 text-sm">
                <input
                  type="checkbox"
                  checked={filters.workflow_tags?.includes(etiqueta.name) || false}
                  onChange={(e) => handleEtiquetaChange(etiqueta.name, e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-gray-700">{etiqueta.name}</span>
              </label>
            ))}
            {(!filterOptions?.etiquetas || filterOptions.etiquetas.length === 0) && (
              <div className="text-sm text-gray-500">No hay etiquetas disponibles</div>
            )}
          </div>
        )}
      </div>

      {/* Resumen de filtros activos */}
      {hasActiveFilters() && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {filters.estado?.map((estado: EstadoEvaluacion) => (
              <span
                key={estado}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                {estadoOptions.find(opt => opt.value === estado)?.label}
                <button
                  onClick={() => handleEstadoChange(estado, false)}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            ))}
            
            {filters.solo_con_sugerencias && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Solo con sugerencias
                <button
                  onClick={() => onFilterChange('solo_con_sugerencias', false)}
                  className="ml-1 text-green-600 hover:text-green-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            
            {(filters.puntuacion_min || filters.puntuacion_max) && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Puntuación: {filters.puntuacion_min || 0} - {filters.puntuacion_max || 10}
                <button
                  onClick={() => {
                    onFilterChange('puntuacion_min', undefined);
                    onFilterChange('puntuacion_max', undefined);
                  }}
                  className="ml-1 text-yellow-600 hover:text-yellow-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
