import React from 'react';
import { useMejorasWithFilters } from '../../hooks/useImprovements';

interface EvaluationsImprovementsProps {
  onSelectMejora?: (mejoraId: string) => void;
}

export const EvaluationsImprovements: React.FC<EvaluationsImprovementsProps> = ({
  onSelectMejora
}) => {
  const {
    mejoras,
    total,
    totalPages,
    isLoading,
    isError,
    error,
    filters,
    updateFilters
  } = useMejorasWithFilters();

  const handleFilterChange = (key: string, value: any) => {
    updateFilters({ [key]: value });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const hasPromptChanges = (mejora: any) => {
    return mejora.user_prompt_mejorado || mejora.system_prompt_mejorado;
  };

  if (isError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="text-red-800">Error al cargar mejoras: {error?.message}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Gestión de Mejoras</h2>
        
        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Agente
              </label>
              <input
                type="text"
                value={filters.agente_id?.join(',') || ''}
                onChange={(e) => handleFilterChange('agente', e.target.value)}
                placeholder="Nombre del agente"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Fecha Desde
              </label>
              <input
                type="date"
                value={filters.fecha_desde}
                onChange={(e) => handleFilterChange('fecha_desde', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Fecha Hasta
              </label>
              <input
                type="date"
                value={filters.fecha_hasta}
                onChange={(e) => handleFilterChange('fecha_hasta', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div className="mt-4 flex justify-between items-center">
            <button
              onClick={() => updateFilters({ agente_id: [], fecha_desde: undefined, fecha_hasta: undefined })}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
            >
              Limpiar filtros
            </button>
            <div className="text-sm text-gray-500">
              {total} mejoras encontradas
            </div>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center h-32">
          <div className="text-lg">Cargando mejoras...</div>
        </div>
      )}

      {/* Cards Grid */}
      {!isLoading && mejoras.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {mejoras.map((mejora) => (
            <div key={mejora.id} className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {mejora.nombre_agente_amigable || mejora.agente_id}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {formatDate(mejora.created_at)}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  {hasPromptChanges(mejora) && (
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      Prompts Mejorados
                    </span>
                  )}
                </div>
              </div>

              {/* Prompt Changes Summary */}
              <div className="space-y-3 mb-4">
                {mejora.user_prompt_mejorado && (
                  <div className="bg-blue-50 p-3 rounded-md">
                    <div className="text-sm font-medium text-blue-900 mb-1">
                      User Prompt Mejorado
                    </div>
                    <div className="text-sm text-blue-800 line-clamp-2">
                      {mejora.user_prompt_mejorado.substring(0, 150)}...
                    </div>
                  </div>
                )}
                
                {mejora.system_prompt_mejorado && (
                  <div className="bg-purple-50 p-3 rounded-md">
                    <div className="text-sm font-medium text-purple-900 mb-1">
                      System Prompt Mejorado
                    </div>
                    <div className="text-sm text-purple-800 line-clamp-2">
                      {mejora.system_prompt_mejorado.substring(0, 150)}...
                    </div>
                  </div>
                )}
              </div>

              {/* Recommendations */}
              {mejora.explicacion_mejoras && (
                <div className="bg-yellow-50 p-3 rounded-md mb-4">
                  <div className="text-sm font-medium text-yellow-900 mb-1">
                    Recomendaciones
                  </div>
                  <div className="text-sm text-yellow-800 line-clamp-3">
                    {mejora.explicacion_mejoras}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-between items-center pt-4 border-t">
                <div className="text-sm text-gray-500">
                  ID: {mejora.id.substring(0, 8)}...
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => onSelectMejora?.(mejora.id)}
                    className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800 border border-blue-200 rounded hover:bg-blue-50"
                  >
                    Ver Comparación
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && mejoras.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">No se encontraron mejoras</div>
          <div className="text-gray-400 text-sm mt-2">
            Las mejoras se generan automáticamente cuando se procesan evaluaciones
          </div>
        </div>
      )}

      {/* Pagination */}
      {!isLoading && totalPages > 1 && (
        <div className="flex items-center justify-between bg-white px-4 py-3 border rounded-lg">
          <div className="text-sm text-gray-700">
            Mostrando {((filters.page - 1) * filters.page_size) + 1} a {Math.min(filters.page * filters.page_size, total)} de {total} resultados
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => updateFilters({ page: Math.max(filters.page - 1, 1) })}
              disabled={filters.page === 1}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Anterior
            </button>
            <span className="px-3 py-1 text-sm bg-blue-50 text-blue-600 border rounded">
              {filters.page} de {totalPages}
            </span>
            <button
              onClick={() => updateFilters({ page: Math.min(filters.page + 1, totalPages) })}
              disabled={filters.page === totalPages}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Siguiente
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
