from pydantic import BaseModel, Field
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from decimal import Decimal
from enum import Enum

class EstadoEvaluacion(str, Enum):
    """Estados posibles para una evaluación."""
    PENDIENTE_REVISION = "pendiente_revision"
    REVISADO = "revisado"
    PENDIENTE_MEJORAS = "pendiente_mejoras"
    MEJORAS_APLICADAS = "mejoras_aplicadas"

class EvaluacionBase(BaseModel):
    """Modelo base para evaluaciones."""
    nombre_agente_workflow: str
    proposito_agente: str
    criterios_adicionales_evaluacion: Optional[str] = None
    agente_output: str
    puntuacion: Decimal
    argumentos_puntuacion: str
    sugerencias_mejora: Optional[str] = None
    execution_id: str
    n8n_workflow_id: str
    estado: EstadoEvaluacion = EstadoEvaluacion.PENDIENTE_REVISION
    agente_id: str
    nombre_agente_amigable: str
    mejora_agente_id: Optional[UUID] = None

class EvaluacionCreate(EvaluacionBase):
    """Modelo para crear una nueva evaluación."""
    pass

class EvaluacionUpdate(BaseModel):
    """Modelo para actualizar una evaluación."""
    nombre_agente_workflow: Optional[str] = None
    proposito_agente: Optional[str] = None
    criterios_adicionales_evaluacion: Optional[str] = None
    agente_output: Optional[str] = None
    puntuacion: Optional[Decimal] = None
    argumentos_puntuacion: Optional[str] = None
    sugerencias_mejora: Optional[str] = None
    execution_id: Optional[str] = None
    n8n_workflow_id: Optional[str] = None
    estado: Optional[EstadoEvaluacion] = None
    agente_id: Optional[str] = None
    nombre_agente_amigable: Optional[str] = None
    mejora_agente_id: Optional[UUID] = None

class EvaluacionInDB(EvaluacionBase):
    """Modelo para evaluaciones en la base de datos."""
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

class Evaluacion(EvaluacionInDB):
    """Modelo completo de evaluación para respuestas API."""
    pass

class EvaluacionListItem(BaseModel):
    """Modelo simplificado para listas de evaluaciones."""
    id: int
    nombre_agente_amigable: Optional[str] = None
    nombre_agente_workflow: str
    puntuacion: Decimal
    estado: EstadoEvaluacion
    created_at: datetime
    n8n_workflow_id: str
    execution_id: str
    mejora_agente_id: Optional[UUID] = None
    sugerencias_mejora: Optional[str] = None

class EvaluacionFilters(BaseModel):
    """Filtros para la lista de evaluaciones."""
    estado: Optional[List[EstadoEvaluacion]] = None
    workflow_tags: Optional[List[str]] = None
    agente_id: Optional[List[str]] = None
    puntuacion_min: Optional[Decimal] = None
    puntuacion_max: Optional[Decimal] = None
    fecha_desde: Optional[datetime] = None
    fecha_hasta: Optional[datetime] = None

class EvaluacionSortBy(str, Enum):
    """Opciones de ordenamiento para evaluaciones."""
    PUNTUACION_ASC = "puntuacion_asc"
    PUNTUACION_DESC = "puntuacion_desc"
    FECHA_ASC = "fecha_asc"
    FECHA_DESC = "fecha_desc"
    AGENTE_ASC = "agente_asc"
    AGENTE_DESC = "agente_desc"

class EvaluacionGroupBy(str, Enum):
    """Opciones de agrupación para evaluaciones."""
    WORKFLOW = "workflow"
    AGENTE = "agente"
    ESTADO = "estado"
    NINGUNO = "ninguno"

class EvaluacionListResponse(BaseModel):
    """Respuesta para la lista de evaluaciones."""
    evaluaciones: List[EvaluacionListItem]
    total: int
    page: int
    page_size: int
    total_pages: int

class EvaluacionBatchUpdate(BaseModel):
    """Modelo para actualizaciones en lote."""
    evaluacion_ids: List[int]
    estado: EstadoEvaluacion

class EvaluacionStats(BaseModel):
    """Estadísticas de evaluaciones para el dashboard."""
    total_evaluaciones: int
    promedio_puntuacion: Decimal
    evaluaciones_por_estado: dict[EstadoEvaluacion, int]
    evaluaciones_por_agente: dict[str, int]
    tendencia_puntuacion: List[dict]  # Para gráficos temporales

class EvaluacionDashboardData(BaseModel):
    """Datos completos para el dashboard de evaluaciones."""
    stats: EvaluacionStats
    evaluaciones_recientes: List[EvaluacionListItem]
    agentes_performance: List[dict]  # Performance por agente
    workflow_performance: List[dict]  # Performance por workflow
